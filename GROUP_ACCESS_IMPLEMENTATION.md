# Group-Based Access Control for Private Documents - Community Version

## Overview

This implementation adds proper group-based access control for private documents in the community version of Onyx. Previously, the community version ignored group information during indexing and retrieval, making private documents assigned to user teams inaccessible to team members.

## Problem Statement

The core issue was that the community version of Onyx did not properly enforce group-based access control for private connectors and document sets, even when they were assigned to `user_team` or `user_group`. The system's access logic explicitly ignored group information during indexing and retrieval.

## Solution

The implementation adds the following key components:

### 1. User Group ID Retrieval Function (`backend/onyx/db/user_teams.py`)

```python
def get_user_group_ids_for_user(
    db_session: Session, user_id: UUID, only_up_to_date: bool = True
) -> list[int]:
    """
    Gets the IDs of user groups that a specific user belongs to.
    This is used for access control to determine which groups a user has access to.
    """
```

### 2. Document Group Association Function (`backend/onyx/db/document.py`)

```python
def get_user_group_ids_for_documents(
    db_session: Session,
    document_ids: list[str],
) -> dict[str, list[int]]:
    """
    Fetches all user group IDs that have access to the given documents.
    Returns a dictionary mapping document_id to list of user_group_ids.
    """
```

### 3. Group ID Prefix Utility (`backend/onyx/access/utils.py`)

```python
def prefix_user_group_id(user_group_id: int) -> str:
    """Prefixes a user group ID to eliminate collision with user emails and group names."""
    return f"group_id:{user_group_id}"
```

### 4. Enhanced Document Access Logic (`backend/onyx/access/access.py`)

- **Updated `_get_access_for_documents`**: Now fetches user group information and includes it in `DocumentAccess` objects
- **Updated `_get_acl_for_user`**: Now includes the user's group memberships in their Access Control List

### 5. Enhanced ACL Generation (`backend/onyx/access/models.py`)

- **Updated `DocumentAccess.to_acl()`**: Now handles both group IDs (community version) and group names (enterprise version)

## Key Features

### Backward Compatibility
- The implementation maintains compatibility with the enterprise version
- Handles both group IDs (numeric strings) and group names in the same ACL system
- No breaking changes to existing APIs

### Consistent Prefixing
- Group IDs use the prefix `group_id:` to avoid collisions
- Group names use the prefix `group:` (existing enterprise behavior)
- User emails use the prefix `user_email:` (existing behavior)

### Proper Access Control
- Users can now access private documents assigned to their teams
- Public documents remain accessible to all users
- Private documents without group assignments remain restricted to specific users

## How It Works

1. **Document Indexing**: When documents are indexed, the system now retrieves and stores user group associations
2. **User Authentication**: When a user makes a request, the system fetches their group memberships
3. **Access Control**: The system matches user group memberships against document group assignments
4. **Document Retrieval**: Only documents with matching access permissions are returned

## Testing

The implementation includes comprehensive tests that verify:
- Group ID prefixing works correctly
- Document ACL generation includes group information
- Public documents remain accessible
- Mixed group types (IDs and names) work together

## Usage Example

```python
# User belongs to groups 1, 2, 3
user_acl = get_acl_for_user(user, db_session)
# Returns: {'user_email:<EMAIL>', 'group_id:1', 'group_id:2', 'group_id:3', 'PUBLIC'}

# Document is assigned to group 2
document_acl = document_access.to_acl()
# Returns: {'group_id:2', 'user_email:<EMAIL>'}

# Access granted because 'group_id:2' is in both ACLs
```

## Files Modified

1. `backend/onyx/db/user_teams.py` - Added user group ID retrieval function
2. `backend/onyx/db/document.py` - Added document group association function
3. `backend/onyx/access/utils.py` - Added group ID prefix utility
4. `backend/onyx/access/access.py` - Enhanced access logic with group support
5. `backend/onyx/access/models.py` - Enhanced ACL generation for mixed group types

## Benefits

- **Security**: Proper enforcement of group-based permissions
- **Usability**: Team members can access documents assigned to their teams
- **Consistency**: Unified access control system across community and enterprise versions
- **Performance**: Efficient group membership queries and ACL generation
