# Community Group-Based Access Control Solution

## Problem Solved

The issue was that users could not retrieve private documents assigned to their teams in the community version. The search logs showed that the user's ACL only contained:
- `"PUBLIC"`
- `"user_email:<EMAIL>"`

But was missing the user's group memberships like `"group_id:1"`, `"group_id:2"`, etc.

## Root Cause

The system was using the Enterprise Edition version of the ACL function through the versioned implementation system, which doesn't include our community version improvements for group-based access control.

## Solution Implemented

### 1. Community-Specific Access Module (`backend/onyx/access/community_access.py`)

Created a dedicated module that provides group-based access control for the community version, working independently of the EE versioned implementation system.

**Key Functions:**
- `get_community_acl_for_user()` - Generates ACL including user group memberships
- `build_community_access_filters_for_user()` - Drop-in replacement for access filter building

### 2. Enhanced Access Filter Building (`backend/onyx/context/search/preprocessing/access_filters.py`)

Modified the `build_access_filters_for_user()` function to:
- Always use the community access control that includes group memberships
- Optionally include EE-specific ACL entries if EE is enabled
- Ensure group-based access works regardless of EE status

### 3. Supporting Infrastructure

**User Group Utilities (`backend/onyx/db/user_group_utils.py`):**
- `get_user_group_ids_for_user()` - Retrieves group IDs for a user (separated to avoid circular imports)

**Document Group Association (`backend/onyx/db/document.py`):**
- `get_user_group_ids_for_documents()` - Fetches group IDs associated with documents

**Access Utilities (`backend/onyx/access/utils.py`):**
- `prefix_user_group_id()` - Consistent formatting for group ID ACL entries

**Enhanced ACL Generation (`backend/onyx/access/models.py`):**
- Updated `DocumentAccess.to_acl()` to handle both group IDs and group names

## How It Works

1. **User Request**: When a user makes a search request
2. **ACL Generation**: `build_access_filters_for_user()` calls `get_community_acl_for_user()`
3. **Group Membership**: The function fetches the user's group IDs using `get_user_group_ids_for_user()`
4. **ACL Building**: Creates ACL entries like:
   - `"user_email:<EMAIL>"`
   - `"group_id:1"`
   - `"group_id:2"`
   - `"PUBLIC"`
5. **Document Matching**: Documents with matching group assignments are accessible

## Expected Result

After this implementation, the search logs should show ACL entries like:
```
access_control_list=['PUBLIC', 'user_email:<EMAIL>', 'group_id:1', 'group_id:2']
```

This will allow users to access private documents assigned to their teams.

## Files Modified

1. **NEW**: `backend/onyx/access/community_access.py` - Community-specific access control
2. **MODIFIED**: `backend/onyx/context/search/preprocessing/access_filters.py` - Enhanced filter building
3. **NEW**: `backend/onyx/db/user_group_utils.py` - User group utilities (separated to avoid circular imports)
4. **MODIFIED**: `backend/onyx/db/document.py` - Added document group association function
5. **MODIFIED**: `backend/onyx/access/utils.py` - Added group ID prefix utility
6. **MODIFIED**: `backend/onyx/access/models.py` - Enhanced ACL generation for mixed group types

## Key Benefits

- **No EE File Modifications**: Solution works entirely within community version files
- **EE Compatibility**: Works alongside EE features when enabled
- **Circular Import Prevention**: Clean separation of concerns
- **Backward Compatibility**: Doesn't break existing functionality
- **Comprehensive**: Handles both document access and user ACL generation

## Testing

The implementation includes:
- Proper group ID prefixing (`group_id:123`)
- User email prefixing (`user_email:<EMAIL>`)
- Public document access (`PUBLIC`)
- Mixed group type handling (IDs and names)
- Error handling for database failures

## Next Steps

1. Restart the application to load the new access control logic
2. Test with a user who belongs to teams with private documents
3. Verify that the search logs now include group ID entries in the ACL
4. Confirm that users can access private documents assigned to their teams
