"""
Community-specific access control functions.
This module provides group-based access control for the community version,
working independently of the Enterprise Edition versioned implementation system.
"""

from sqlalchemy.orm import Session

from onyx.access.utils import prefix_user_email, prefix_user_group_id
from onyx.configs.constants import PUBLIC_DOC_PAT
from onyx.db.models import User
from onyx.db.user_group_utils import get_user_group_ids_for_user


def get_community_acl_for_user(user: User | None, db_session: Session) -> set[str]:
    """
    Community version of ACL generation that includes user group memberships.
    This function works independently of the EE versioned implementation system.
    
    Returns a set of ACL entries that the user has access to, including:
    - User email
    - User group IDs (prefixed)
    - Public document pattern
    
    Args:
        user: The user to generate ACL for
        db_session: Database session
        
    Returns:
        Set of ACL entries for the user
    """
    if user:
        acl_entries = {prefix_user_email(user.email), PUBLIC_DOC_PAT}
        
        # Add user group memberships to ACL
        try:
            user_group_ids = get_user_group_ids_for_user(db_session, user.id)
            for group_id in user_group_ids:
                acl_entries.add(prefix_user_group_id(group_id))
        except Exception as e:
            # Log the error but don't fail the entire ACL generation
            import logging
            logger = logging.getLogger(__name__)
            logger.warning(f"Failed to get user group IDs for user {user.id}: {e}")
        
        return acl_entries
    return {PUBLIC_DOC_PAT}


def build_community_access_filters_for_user(user: User | None, session: Session) -> list[str]:
    """
    Community version of access filter building that includes user group memberships.
    This can be used as a drop-in replacement for build_access_filters_for_user.
    
    Args:
        user: The user to build filters for
        session: Database session
        
    Returns:
        List of ACL entries for filtering
    """
    user_acl = get_community_acl_for_user(user, session)
    return list(user_acl)
