"""
Utility functions for user group operations.
This module is separate to avoid circular imports with access control modules.
"""

from uuid import UUID
from sqlalchemy import select
from sqlalchemy.orm import Session

from onyx.db.models import User, UserGroup, User__UserGroup


def get_user_group_ids_for_user(
    db_session: Session, user_id: UUID, only_up_to_date: bool = True
) -> list[int]:
    """
    Gets the IDs of user groups that a specific user belongs to.
    This is used for access control to determine which groups a user has access to.
    
    Args:
        db_session: Database session
        user_id: UUID of the user
        only_up_to_date: Whether to only include up-to-date groups
        
    Returns:
        List of user group IDs that the user belongs to
    """
    stmt = (
        select(UserGroup.id)
        .join(User__UserGroup, User__UserGroup.user_group_id == UserGroup.id)
        .join(User, User.id == User__UserGroup.user_id)  # type: ignore
        .where(User.id == user_id)  # type: ignore
    )
    if only_up_to_date:
        stmt = stmt.where(UserGroup.is_up_to_date == True)  # noqa: E712
    return list(db_session.scalars(stmt).all())
