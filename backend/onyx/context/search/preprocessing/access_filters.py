from sqlalchemy.orm import Session

from onyx.access.access import get_acl_for_user
from onyx.access.community_access import get_community_acl_for_user
from onyx.context.search.models import IndexFilters
from onyx.db.models import User
from onyx.utils.variable_functionality import global_version


def build_access_filters_for_user(user: User | None, session: Session) -> list[str]:
    # Use community access control that includes group memberships
    # This ensures group-based access works regardless of EE status
    user_acl = get_community_acl_for_user(user, session)

    # If EE is enabled, also include EE-specific ACL entries
    if global_version.is_ee_version():
        try:
            ee_acl = get_acl_for_user(user, session)
            user_acl.update(ee_acl)
        except Exception:
            # If EE ACL fails, continue with community ACL
            pass

    return list(user_acl)


def build_user_only_filters(user: User | None, db_session: Session) -> IndexFilters:
    user_acl_filters = build_access_filters_for_user(user, db_session)
    return IndexFilters(
        source_type=None,
        document_set=None,
        time_cutoff=None,
        tags=None,
        access_control_list=user_acl_filters,
    )
